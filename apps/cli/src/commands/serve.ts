import { Command } from 'commander';
import chalk from 'chalk';
import { spawn } from 'child_process';
import * as path from 'path';

export const serveCommand = new Command('serve')
    .description('Start the localhost API server')
    .option('-p, --port <number>', 'Port to run the server on', '3001')
    .option('-h, --host <string>', 'Host to bind the server to', 'localhost')
    .action(async (options) => {
        try {
            console.log(chalk.cyan.bold('\n🚀 Starting API Server\n'));

            const port = parseInt(options.port, 10);
            const host = options.host;

            // Validate port
            if (isNaN(port) || port < 1 || port > 65535) {
                console.error(chalk.red('✗ Invalid port number. Port must be between 1 and 65535.'));
                process.exit(1);
            }

            // Validate host
            if (!host || host.trim().length === 0) {
                console.error(chalk.red('✗ Invalid host. Host cannot be empty.'));
                process.exit(1);
            }

            console.log(chalk.cyan('--- Server Configuration ---'));
            console.log(chalk.gray('Host:'), chalk.white(host));
            console.log(chalk.gray('Port:'), chalk.white(port));
            console.log(chalk.gray('Environment:'), chalk.white(process.env.NODE_ENV || 'development'));

            console.log(chalk.cyan('\n--- Starting Server ---'));
            console.log(chalk.gray('This will start the Ever Works API server locally.'));
            console.log(chalk.gray('The server will be available at:'), chalk.white(`http://${host}:${port}`));

            // Try to find the API application
            const apiPath = path.resolve(process.cwd(), '../../apps/api');
            
            console.log(chalk.gray('\nStarting server from:'), chalk.white(apiPath));

            // Start the API server using npm/pnpm
            const serverProcess = spawn('pnpm', ['run', 'start:dev'], {
                cwd: apiPath,
                stdio: 'inherit',
                env: {
                    ...process.env,
                    PORT: port.toString(),
                    HOST: host,
                }
            });

            console.log(chalk.green('\n✓ Server process started'));
            console.log(chalk.yellow('\n--- Controls ---'));
            console.log(chalk.gray('Press'), chalk.white('Ctrl+C'), chalk.gray('to stop the server'));

            // Handle process termination
            process.on('SIGINT', () => {
                console.log(chalk.yellow('\n\n⚠ Shutting down server...'));
                serverProcess.kill('SIGTERM');
                setTimeout(() => {
                    console.log(chalk.green('✓ Server stopped successfully'));
                    process.exit(0);
                }, 1000);
            });

            process.on('SIGTERM', () => {
                console.log(chalk.yellow('\n\n⚠ Shutting down server...'));
                serverProcess.kill('SIGTERM');
                setTimeout(() => {
                    console.log(chalk.green('✓ Server stopped successfully'));
                    process.exit(0);
                }, 1000);
            });

            // Handle server process exit
            serverProcess.on('exit', (code) => {
                if (code !== 0) {
                    console.error(chalk.red(`\n✗ Server process exited with code ${code}`));
                    process.exit(code || 1);
                } else {
                    console.log(chalk.green('\n✓ Server stopped successfully'));
                    process.exit(0);
                }
            });

            serverProcess.on('error', (error) => {
                console.error(chalk.red('\n✗ Failed to start server:'), error.message);
                
                if (error.message.includes('ENOENT')) {
                    console.log(chalk.yellow('\n💡 Tips:'));
                    console.log(chalk.gray('  • Make sure you are in the correct directory'));
                    console.log(chalk.gray('  • Ensure the API application exists at ../../apps/api'));
                    console.log(chalk.gray('  • Check that pnpm is installed and available'));
                }
                
                process.exit(1);
            });

        } catch (error) {
            console.error(chalk.red('\n✗ Failed to start API server:'), error.message);

            if (error.code === 'EADDRINUSE') {
                console.log(chalk.yellow('\n💡 Tip: The port might already be in use.'));
                console.log(chalk.gray('Try using a different port with:'), chalk.cyan('--port <number>'));
            }

            process.exit(1);
        }
    });
