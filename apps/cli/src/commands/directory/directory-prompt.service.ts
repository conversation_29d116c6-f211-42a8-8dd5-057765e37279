import chalk from 'chalk';
import inquirer from 'inquirer';
import {
    DirectoryPromptService as BaseDirectoryPromptService,
    DirectorySelection,
    Directory
} from '@packages/cli-shared';
import { getHttpClient } from '../../services/http-client';

// Re-export types from shared package for convenience
export type { DirectoryInputData, MarkdownReadmeConfigDto, SlugConflictResolution, DirectorySelection, Directory } from '@packages/cli-shared';

export class DirectoryPromptService extends BaseDirectoryPromptService {
    private httpClient = getHttpClient(); // Will be used when API endpoint is available

    /**
     * Override the base implementation to handle API-based directory selection
     * For now, this is a placeholder until the API endpoint is implemented
     */
    async promptDirectorySelection(): Promise<DirectorySelection> {
        try {
            // TODO: Replace with actual API call when GET /directories endpoint is available
            // const response = await this.httpClient.get('/directories');
            // const directories = response.data.directories || [];
            // return super.promptDirectorySelection(directories);

            console.log(chalk.yellow('⚠ Directory listing not yet implemented in API.'));
            console.log(chalk.gray('Please enter the directory slug manually:'));

            const answer = await inquirer.prompt([
                {
                    type: 'input',
                    name: 'slug',
                    message: 'Directory slug:',
                    validate: (input) => input.trim().length > 0 || 'Directory slug is required'
                }
            ]);

            // Mock directory object for now
            const directory: Directory = {
                id: 1,
                name: answer.slug,
                slug: answer.slug,
                owner: 'unknown',
                organization: false,
                description: 'Directory selected by slug'
            };

            return { directory, cancelled: false };
        } catch (error) {
            console.error(chalk.red('Error selecting directory:'), error.message);
            return { directory: null, cancelled: true };
        }
    }
}
