import { Command } from 'commander';
import { setupCommand } from './setup';
import { showCommand } from './show';
import { testCommand } from './test';
import { setCommand } from './set';
import { unsetCommand } from './unset';
import { switchAiCommand } from './switch-ai';

export const configCommand = new Command('config')
    .description('Configuration management commands')
    .addCommand(setupCommand)
    .addCommand(showCommand)
    .addCommand(testCommand)
    .addCommand(setCommand)
    .addCommand(unsetCommand)
    .addCommand(switchAiCommand)
    .action(() => {
        console.log('Available config commands:');
        console.log('  setup      - Setup Ever Works CLI configuration');
        console.log('  show       - Show current configuration');
        console.log('  test       - Test configuration connectivity');
        console.log('  set        - Set a configuration value');
        console.log('  unset      - Remove a configuration value');
        console.log('  switch-ai  - Switch between configured AI providers');
        console.log('\nUse "config <command> --help" for more information about a command.');
    });
