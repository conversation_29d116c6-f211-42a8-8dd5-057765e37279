{"name": "ever-works-cli", "version": "1.0.0", "description": "", "main": "index.js", "bin": {"ever-works": "dist/cli.js"}, "scripts": {"build": "node build.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.13.1", "devDependencies": {"@types/node": "^22.16.3", "esbuild": "^0.25.6", "typescript": "^5.8.3"}, "dependencies": {"commander": "^14.0.0"}}